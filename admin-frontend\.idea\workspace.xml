<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ceceb9c1-aa96-47eb-91ba-516bec4abe9c" name="更改" comment="工作流">
      <change beforePath="$PROJECT_DIR$/components.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/components.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/FeishuDocumentViewer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/FeishuDocumentViewer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/Feishu.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/Feishu.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../backend/src/main/java/com/lait/controller/FeishuController.java" beforeDir="false" afterPath="$PROJECT_DIR$/../backend/src/main/java/com/lait/controller/FeishuController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../backend/src/main/java/com/lait/service/impl/FeishuServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../backend/src/main/java/com/lait/service/impl/FeishuServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../backend/target/classes/com/lait/controller/FeishuController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../backend/target/classes/com/lait/controller/FeishuController.class" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2xnXuoX3QVuNN1SLwchoMqLaQ74" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Node.js.vite.config.js.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/code-test/LAIT (Learning Assistance AI Tools)/admin-frontend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "D:\\App\\IntelliJ IDEA 2025.1.1.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ceceb9c1-aa96-47eb-91ba-516bec4abe9c" name="更改" comment="" />
      <created>1748575036727</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748575036727</updated>
      <workItem from="1748575039191" duration="26025000" />
    </task>
    <task id="LOCAL-00001" summary="工作流">
      <option name="closed" value="true" />
      <created>1748932264489</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748932264489</updated>
    </task>
    <task id="LOCAL-00002" summary="工作流">
      <option name="closed" value="true" />
      <created>1748932886007</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748932886007</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="工作流" />
    <option name="LAST_COMMIT_MESSAGE" value="工作流" />
  </component>
</project>