<template>
  <div class="document-edit-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button
          :icon="ArrowLeft"
          @click="goBack"
          type="text"
          size="large"
        >
          返回
        </el-button>
        <el-divider direction="vertical" />
        <h1 v-if="document">编辑文档: {{ document.title }}</h1>
        <el-skeleton v-else animated>
          <template #template>
            <el-skeleton-item variant="h1" style="width: 300px" />
          </template>
        </el-skeleton>
      </div>
      <div class="header-right">
        <el-button
          type="success"
          :icon="Check"
          @click="saveDocument"
          :loading="saving"
        >
          保存文档
        </el-button>
        <el-button
          v-if="document"
          type="primary"
          :icon="View"
          @click="previewDocument"
        >
          预览
        </el-button>
        <el-button
          v-if="document"
          type="success"
          :icon="Download"
          @click="exportToPdf"
          :loading="exportingPdf"
        >
          导出PDF
        </el-button>
      </div>
    </div>

    <!-- 编辑器容器 -->
    <div class="editor-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton animated>
          <template #template>
            <el-skeleton-item variant="rect" style="height: 400px" />
          </template>
        </el-skeleton>
      </div>

      <div v-else-if="error" class="error-container">
        <el-result
          icon="error"
          :title="error"
          sub-title="请检查文档ID是否正确，或稍后重试"
        >
          <template #extra>
            <el-button type="primary" @click="loadDocument">重新加载</el-button>
            <el-button @click="goBack">返回</el-button>
          </template>
        </el-result>
      </div>

      <div v-else-if="document" class="editor-wrapper">
        <!-- 编辑模式选择 -->
        <div class="editor-toolbar">
          <el-radio-group v-model="editMode" @change="handleModeChange">
            <el-radio-button label="markdown">Markdown编辑器</el-radio-button>
            <el-radio-button label="html">HTML编辑器</el-radio-button>
            <el-radio-button label="split">分屏编辑</el-radio-button>
            <el-radio-button label="preview">预览</el-radio-button>
          </el-radio-group>

          <div class="toolbar-actions">
            <el-button
              size="small"
              :icon="Refresh"
              @click="resetContent"
              title="重置内容"
            >
              重置
            </el-button>
            <el-button
              size="small"
              :icon="DocumentCopy"
              @click="copyContent"
              title="复制内容"
            >
              复制
            </el-button>
          </div>
        </div>

        <!-- 编辑器主体 -->
        <div class="editor-main" :class="{ 'split-view': editMode === 'split' }">
          <!-- Markdown编辑器 -->
          <div v-if="editMode === 'markdown'" class="editor-panel">
            <div class="bytemd-wrapper">
              <Editor
                :value="editContent"
                :plugins="markdownPlugins"
                @change="handleMarkdownChange"
                :locale="locale"
                class="markdown-editor-component"
              />
            </div>
          </div>

          <!-- HTML编辑器 -->
          <div v-else-if="editMode === 'html'" class="editor-panel">
            <div class="monaco-wrapper">
              <div ref="monacoEditor" class="monaco-editor-container"></div>
            </div>
          </div>

          <!-- 分屏模式 -->
          <div v-else-if="editMode === 'split'" class="split-editor">
            <div class="split-left">
              <div class="split-header">编辑</div>
              <el-input
                v-model="editContent"
                type="textarea"
                :rows="20"
                placeholder="请输入Markdown内容..."
                class="split-textarea"
                @input="handleContentChange"
              />
            </div>
            <div class="split-divider"></div>
            <div class="split-right">
              <div class="split-header">预览</div>
              <div class="split-preview" v-html="previewContent"></div>
            </div>
          </div>

          <!-- 预览面板 -->
          <div v-else-if="editMode === 'preview'" class="preview-panel">
            <!-- 预览工具栏 -->
            <div class="preview-toolbar">
              <div class="preview-mode-switch">
                <el-radio-group v-model="pdfPreviewMode" size="small">
                  <el-radio-button :label="false">普通预览</el-radio-button>
                  <el-radio-button :label="true">PDF分页预览</el-radio-button>
                </el-radio-group>
              </div>

              <!-- PDF分页控制 -->
              <div v-if="pdfPreviewMode" class="pdf-pagination">
                <el-button
                  size="small"
                  :disabled="currentPage <= 1"
                  @click="currentPage--"
                >
                  上一页
                </el-button>
                <span class="page-info">
                  {{ currentPage }} / {{ totalPages }}
                </span>
                <el-button
                  size="small"
                  :disabled="currentPage >= totalPages"
                  @click="currentPage++"
                >
                  下一页
                </el-button>
              </div>

              <div class="preview-actions">
                <el-button
                  size="small"
                  type="success"
                  :icon="Download"
                  @click="exportToPdf"
                  :loading="exportingPdf"
                >
                  导出PDF
                </el-button>
              </div>
            </div>

            <!-- 预览内容 -->
            <div class="preview-content-wrapper">
              <!-- 普通预览 -->
              <div v-if="!pdfPreviewMode" class="preview-content" v-html="previewContent"></div>

              <!-- PDF分页预览 -->
              <div v-else class="pdf-preview-container">
                <div class="pdf-page-container">
                  <div
                    v-for="(page, index) in pdfPages"
                    :key="index"
                    v-show="index + 1 === currentPage"
                    class="pdf-page"
                    :style="{
                      width: pageWidth + 'px',
                      minHeight: pageHeight + 'px',
                      maxHeight: pageHeight + 'px'
                    }"
                  >
                    <div class="pdf-page-content" v-html="page.content"></div>
                    <div class="pdf-page-number">第 {{ index + 1 }} 页</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文档预览"
      width="80%"
      :before-close="closePreviewDialog"
    >
      <div class="preview-dialog-content">
        <div class="preview-header">
          <h2>{{ document?.title }}</h2>
          <div class="preview-meta">
            <el-tag type="primary" size="small">{{ getDocTypeText(document?.docType) }}</el-tag>
            <span class="meta-item">最后更新: {{ formatDateTime(new Date()) }}</span>
          </div>
        </div>
        <el-divider />
        <div class="preview-body" v-html="previewContent"></div>
      </div>
      <template #footer>
        <el-button @click="closePreviewDialog">关闭</el-button>
        <el-button type="primary" @click="saveDocument" :loading="saving">
          保存并关闭
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Check,
  View,
  Refresh,
  DocumentCopy,
  Download
} from '@element-plus/icons-vue'
import {
  getFeishuDocument,
  updateFeishuDocumentContent
} from '@/api/feishu'
import { marked } from 'marked'

// 编辑器相关导入
import { Editor } from '@bytemd/vue-next'
import gfm from '@bytemd/plugin-gfm'
import highlight from '@bytemd/plugin-highlight'
import * as monaco from 'monaco-editor'

// PDF相关导入
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

// 样式导入
import 'bytemd/dist/index.css'
import 'highlight.js/styles/default.css'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const document = ref(null)
const error = ref('')
const editMode = ref('markdown')
const editContent = ref('')
const originalContent = ref('')
const hasUnsavedChanges = ref(false)
const previewDialogVisible = ref(false)
const showPreview = ref(false)
const exportingPdf = ref(false)

// PDF预览相关
const pdfPreviewMode = ref(false)
const currentPage = ref(1)
const totalPages = ref(1)
const pdfPages = ref([])
const pageHeight = ref(842) // A4页面高度 (px)
const pageWidth = ref(595) // A4页面宽度 (px)

// 编辑器相关
const monacoEditor = ref(null)
const monacoInstance = ref(null)

// Markdown编辑器配置
const markdownPlugins = [
  gfm(),
  highlight()
]

const locale = {
  // 中文本地化配置
  toolbar: {
    bold: '粗体',
    italic: '斜体',
    strikethrough: '删除线',
    heading: '标题',
    code: '代码',
    quote: '引用',
    link: '链接',
    image: '图片',
    table: '表格',
    list: '列表',
    ordered_list: '有序列表',
    unordered_list: '无序列表',
    task_list: '任务列表',
    fullscreen: '全屏',
    preview: '预览',
    write: '编辑',
    toc: '目录',
    help: '帮助'
  }
}

// 计算属性
const previewContent = computed(() => {
  if (!editContent.value) return ''

  try {
    if (editMode.value === 'html') {
      return editContent.value
    } else {
      // Markdown模式
      marked.setOptions({
        breaks: true,
        gfm: true,
        headerIds: false,
        mangle: false
      })
      return marked(editContent.value)
    }
  } catch (err) {
    console.error('内容渲染失败:', err)
    return `<pre>${editContent.value}</pre>`
  }
})

// 方法
const loadDocument = async () => {
  const documentId = route.params.id
  if (!documentId) {
    error.value = '文档ID参数缺失'
    return
  }

  loading.value = true
  error.value = ''

  try {
    console.log('加载文档信息:', documentId)
    const response = await getFeishuDocument(documentId)

    if (response) {
      document.value = response
      editContent.value = response.content || ''
      originalContent.value = response.content || ''
      console.log('文档信息加载成功:', document.value)
    } else {
      error.value = '文档数据格式异常'
    }
  } catch (err) {
    console.error('加载文档信息失败:', err)
    error.value = err.response?.data?.message || err.message || '加载文档信息失败'
  } finally {
    loading.value = false
  }
}

const saveDocument = async () => {
  if (!document.value) {
    ElMessage.warning('文档信息不存在')
    return
  }

  if (!editContent.value.trim()) {
    ElMessage.warning('文档内容不能为空')
    return
  }

  saving.value = true

  try {
    await updateFeishuDocumentContent(document.value.id, editContent.value)
    originalContent.value = editContent.value
    hasUnsavedChanges.value = false
    ElMessage.success('文档保存成功')

    if (previewDialogVisible.value) {
      closePreviewDialog()
    }
  } catch (error) {
    console.error('保存文档失败:', error)
    ElMessage.error('保存文档失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

const goBack = async () => {
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要离开吗？',
        '确认离开',
        {
          confirmButtonText: '离开',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return
    }
  }

  router.push('/feishu')
}

const previewDocument = () => {
  previewDialogVisible.value = true
}

const closePreviewDialog = () => {
  previewDialogVisible.value = false
}

const handleModeChange = async (mode) => {
  console.log('编辑模式切换:', mode)

  if (mode === 'html') {
    // 切换到HTML模式时初始化Monaco编辑器
    await nextTick()
    initMonacoEditor()
  } else if (monacoInstance.value) {
    // 切换离开HTML模式时销毁Monaco编辑器
    monacoInstance.value.dispose()
    monacoInstance.value = null
  }
}

// Markdown编辑器变化处理
const handleMarkdownChange = (value) => {
  editContent.value = value
  handleContentChange()
}

// 初始化Monaco编辑器
const initMonacoEditor = () => {
  if (!monacoEditor.value || monacoInstance.value) return

  try {
    monacoInstance.value = monaco.editor.create(monacoEditor.value, {
      value: editContent.value,
      language: 'html',
      theme: 'vs-dark',
      automaticLayout: true,
      fontSize: 14,
      lineNumbers: 'on',
      roundedSelection: false,
      scrollBeyondLastLine: false,
      readOnly: false,
      minimap: { enabled: true },
      wordWrap: 'on',
      folding: true,
      lineDecorationsWidth: 10,
      lineNumbersMinChars: 3,
      glyphMargin: false
    })

    // 监听内容变化
    monacoInstance.value.onDidChangeModelContent(() => {
      editContent.value = monacoInstance.value.getValue()
      handleContentChange()
    })

    console.log('Monaco编辑器初始化成功')
  } catch (error) {
    console.error('Monaco编辑器初始化失败:', error)
    ElMessage.error('HTML编辑器初始化失败')
  }
}

// 更新Monaco编辑器内容
const updateMonacoContent = () => {
  if (monacoInstance.value && editMode.value === 'html') {
    const currentValue = monacoInstance.value.getValue()
    if (currentValue !== editContent.value) {
      monacoInstance.value.setValue(editContent.value)
    }
  }
}

// PDF分页处理
const generatePdfPages = () => {
  if (!previewContent.value) {
    pdfPages.value = []
    totalPages.value = 1
    return
  }

  try {
    // 创建临时容器来测量内容高度
    const tempContainer = document.createElement('div')
    tempContainer.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: ${pageWidth.value - 80}px;
      padding: 40px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background: white;
    `
    tempContainer.innerHTML = previewContent.value
    document.body.appendChild(tempContainer)

    // 计算内容总高度
    const contentHeight = tempContainer.scrollHeight
    const availableHeight = pageHeight.value - 120 // 减去页眉页脚空间
    const pageCount = Math.ceil(contentHeight / availableHeight)

    // 分页处理
    const pages = []
    const children = Array.from(tempContainer.children)

    if (pageCount <= 1) {
      // 单页内容
      pages.push({
        content: previewContent.value,
        pageNumber: 1
      })
    } else {
      // 多页内容 - 简化分页逻辑
      const contentPerPage = Math.ceil(children.length / pageCount)

      for (let i = 0; i < pageCount; i++) {
        const startIndex = i * contentPerPage
        const endIndex = Math.min(startIndex + contentPerPage, children.length)
        const pageChildren = children.slice(startIndex, endIndex)

        const pageContent = pageChildren.map(child => child.outerHTML).join('')
        pages.push({
          content: pageContent,
          pageNumber: i + 1
        })
      }
    }

    // 清理临时容器
    document.body.removeChild(tempContainer)

    pdfPages.value = pages
    totalPages.value = pages.length
    currentPage.value = 1

    console.log(`PDF分页完成，共 ${totalPages.value} 页`)
  } catch (error) {
    console.error('PDF分页处理失败:', error)
    // 降级处理：单页显示
    pdfPages.value = [{
      content: previewContent.value,
      pageNumber: 1
    }]
    totalPages.value = 1
    currentPage.value = 1
  }
}

// PDF导出功能
const exportToPdf = async () => {
  if (!editContent.value) {
    ElMessage.warning('文档内容为空，无法导出PDF')
    return
  }

  exportingPdf.value = true

  try {
    // 创建PDF文档
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'pt',
      format: 'a4',
      compress: true
    })

    // 创建用于PDF生成的临时容器
    const pdfContainer = document.createElement('div')
    pdfContainer.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: ${pageWidth.value}px;
      background: white;
      padding: 40px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
    `

    // 添加文档头部信息
    const headerHtml = `
      <div style="text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #3498db;">
        <h1 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 24px;">
          ${document.value?.title || '飞书文档'}
        </h1>
        <div style="font-size: 14px; color: #666; display: flex; justify-content: space-between; align-items: center;">
          <span>类型: ${getDocTypeText(document.value?.docType)}</span>
          <span>创建者: ${document.value?.creatorName || '未知'}</span>
          <span>导出时间: ${new Date().toLocaleString('zh-CN')}</span>
        </div>
      </div>
    `

    pdfContainer.innerHTML = headerHtml + previewContent.value
    document.body.appendChild(pdfContainer)

    // 使用html2canvas生成图片
    const canvas = await html2canvas(pdfContainer, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: pageWidth.value,
      height: pdfContainer.scrollHeight
    })

    // 计算分页
    const imgWidth = pageWidth.value
    const imgHeight = canvas.height * (pageWidth.value / canvas.width)
    const pageHeight = 842 // A4高度
    const totalPages = Math.ceil(imgHeight / pageHeight)

    // 添加页面到PDF
    for (let i = 0; i < totalPages; i++) {
      if (i > 0) {
        pdf.addPage()
      }

      const yOffset = -(i * pageHeight)
      pdf.addImage(
        canvas.toDataURL('image/jpeg', 0.95),
        'JPEG',
        0,
        yOffset,
        imgWidth,
        imgHeight
      )

      // 添加页码
      pdf.setFontSize(10)
      pdf.setTextColor(128, 128, 128)
      pdf.text(
        `第 ${i + 1} 页 / 共 ${totalPages} 页`,
        imgWidth - 100,
        pageHeight - 20
      )
    }

    // 清理临时容器
    document.body.removeChild(pdfContainer)

    // 保存PDF
    const fileName = `${document.value?.title || '飞书文档'}_${new Date().toISOString().slice(0, 10)}.pdf`
    pdf.save(fileName)

    ElMessage.success('PDF导出成功')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败: ' + error.message)
  } finally {
    exportingPdf.value = false
  }
}

const handleContentChange = () => {
  hasUnsavedChanges.value = editContent.value !== originalContent.value
}

const resetContent = async () => {
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '确定要重置内容吗？这将丢失所有未保存的更改。',
        '确认重置',
        {
          confirmButtonText: '重置',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return
    }
  }

  editContent.value = originalContent.value
  hasUnsavedChanges.value = false
  ElMessage.success('内容已重置')
}

const copyContent = async () => {
  try {
    await navigator.clipboard.writeText(editContent.value)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 工具方法
const getDocTypeText = (type) => {
  const texts = {
    DOC: '文档',
    SHEET: '表格',
    SLIDE: '演示文稿',
    MINDMAP: '思维导图',
    BITABLE: '多维表格'
  }
  return texts[type] || type
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 监听内容变化
watch(editContent, () => {
  handleContentChange()
})

// 监听PDF预览模式变化
watch(pdfPreviewMode, (newValue) => {
  if (newValue && previewContent.value) {
    nextTick(() => {
      generatePdfPages()
    })
  }
})

// 监听预览内容变化
watch(previewContent, () => {
  if (pdfPreviewMode.value) {
    nextTick(() => {
      generatePdfPages()
    })
  }
})

// 页面离开前确认
window.addEventListener('beforeunload', (e) => {
  if (hasUnsavedChanges.value) {
    e.preventDefault()
    e.returnValue = ''
  }
})

// 生命周期
onMounted(() => {
  loadDocument()
})

onUnmounted(() => {
  // 清理Monaco编辑器
  if (monacoInstance.value) {
    monacoInstance.value.dispose()
    monacoInstance.value = null
  }
})
</script>

<style scoped>
.document-edit-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  max-width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-right {
  display: flex;
  gap: 10px;
}

.editor-container {
  flex: 1;
  overflow: hidden;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container {
  padding: 20px;
}

.error-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.editor-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

.editor-main {
  flex: 1;
  overflow: hidden;
  display: flex;
}

.editor-panel {
  flex: 1;
  padding: 20px;
}

.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
  flex-shrink: 0;
}

.preview-mode-switch {
  display: flex;
  align-items: center;
}

.pdf-pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-info {
  font-size: 14px;
  color: #606266;
  margin: 0 10px;
  min-width: 60px;
  text-align: center;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.preview-content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.preview-content {
  line-height: 1.6;
  color: #333;
}

/* PDF预览样式 */
.pdf-preview-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100%;
  background: #f5f5f5;
  padding: 20px;
}

.pdf-page-container {
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.pdf-page {
  position: relative;
  background: white;
  border: 1px solid #ddd;
  overflow: hidden;
  margin: 0 auto;
  box-sizing: border-box;
}

.pdf-page-content {
  padding: 40px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow: hidden;
  height: calc(100% - 40px);
  box-sizing: border-box;
}

.pdf-page-number {
  position: absolute;
  bottom: 10px;
  right: 20px;
  font-size: 12px;
  color: #999;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 8px;
  border-radius: 3px;
}

.split-view .editor-panel {
  flex: 1;
  border-right: 1px solid #ebeef5;
}

.split-view .preview-panel {
  flex: 1;
}

/* 编辑器样式 */
.bytemd-wrapper {
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.markdown-editor-component {
  height: 100%;
}

.monaco-wrapper {
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.monaco-editor-container {
  height: 100%;
  width: 100%;
}

/* 分屏编辑样式 */
.split-editor {
  display: flex;
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.split-left,
.split-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.split-header {
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.split-textarea {
  flex: 1;
}

.split-textarea :deep(.el-textarea__inner) {
  height: 100%;
  border: none;
  border-radius: 0;
  resize: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.split-preview {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background: #fff;
}

.split-divider {
  width: 1px;
  background: #dcdfe6;
  flex-shrink: 0;
}

/* 传统编辑器样式 */
.markdown-editor,
.html-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.markdown-editor :deep(.el-textarea__inner),
.html-editor :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  border: none;
  box-shadow: none;
  resize: none;
}

.preview-content {
  line-height: 1.6;
  color: #333;
}

/* Markdown预览样式 */
.preview-content :deep(h1) {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
  color: #2c3e50;
}

.preview-content :deep(h2) {
  font-size: 24px;
  font-weight: 600;
  margin: 30px 0 15px 0;
  color: #34495e;
}

.preview-content :deep(h3) {
  font-size: 20px;
  font-weight: 600;
  margin: 25px 0 12px 0;
  color: #34495e;
}

.preview-content :deep(p) {
  margin: 0 0 15px 0;
  text-align: justify;
}

.preview-content :deep(ul),
.preview-content :deep(ol) {
  margin: 0 0 15px 0;
  padding-left: 25px;
}

.preview-content :deep(li) {
  margin: 5px 0;
}

.preview-content :deep(blockquote) {
  margin: 15px 0;
  padding: 10px 15px;
  background: #f8f9fa;
  border-left: 4px solid #409eff;
  color: #666;
}

.preview-content :deep(code) {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.preview-content :deep(pre) {
  background: #f6f8fa;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 15px 0;
  border: 1px solid #e1e4e8;
}

.preview-content :deep(pre code) {
  background: none;
  padding: 0;
  font-size: 14px;
  line-height: 1.45;
}

.preview-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.preview-content :deep(th),
.preview-content :deep(td) {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.preview-content :deep(th) {
  background: #f5f5f5;
  font-weight: 600;
}

/* 预览对话框样式 */
.preview-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.preview-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.meta-item {
  color: #909399;
  font-size: 14px;
}

.preview-body {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .header-left {
    width: 100%;
  }

  .header-left h1 {
    max-width: none;
    font-size: 18px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .editor-container {
    margin: 10px;
  }

  .editor-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .toolbar-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .editor-main.split-view {
    flex-direction: column;
  }

  .split-view .editor-panel {
    border-right: none;
    border-bottom: 1px solid #ebeef5;
  }

  /* PDF预览响应式 */
  .preview-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .pdf-pagination {
    width: 100%;
    justify-content: center;
  }

  .preview-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .pdf-page {
    width: 100% !important;
    max-width: 350px;
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
  }

  .pdf-page-content {
    height: auto;
    min-height: 400px;
  }

  .pdf-preview-container {
    padding: 10px;
  }
}
</style>

<style>
/* ByteMD编辑器全局样式覆盖 */
.bytemd {
  height: 100% !important;
}

.bytemd-toolbar {
  border-bottom: 1px solid #dcdfe6 !important;
  background: #fafafa !important;
}

.bytemd-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

.bytemd-preview {
  padding: 15px !important;
}

.bytemd-split {
  border: none !important;
}

.bytemd-toolbar-icon {
  color: #606266 !important;
}

.bytemd-toolbar-icon:hover {
  color: #409eff !important;
  background: #ecf5ff !important;
}

/* Monaco编辑器主题调整 */
.monaco-editor .margin {
  background-color: #1e1e1e !important;
}

.monaco-editor .monaco-editor-background {
  background-color: #1e1e1e !important;
}
</style>
